{"timestamp": "2025-07-19T18:37:06.858008", "validation_results": {"dataset_path": "datasets/training_dataset", "data_yaml_exists": true, "classes_txt_exists": true, "data_yaml_classes": ["naiBa", "na<PERSON><PERSON><PERSON>", "lingZhu", "guaiWu", "xiu<PERSON><PERSON>"], "classes_txt_classes": ["naiBa", "na<PERSON><PERSON><PERSON>", "lingZhu", "guaiWu", "xiu<PERSON><PERSON>"], "config_classes": ["naiBa", "na<PERSON><PERSON><PERSON>", "lingZhu", "guaiWu", "xiu<PERSON><PERSON>"], "issues": [], "recommendations": ["类别顺序一致，无需修复"]}, "summary": {"total_issues": 0, "has_data_yaml": true, "has_classes_txt": true, "is_consistent": true}}