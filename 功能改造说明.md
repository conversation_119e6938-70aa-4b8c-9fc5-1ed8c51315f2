# labelImg标注工具改造说明

## 改造内容

本次改造为labelImg标注工具添加了以下两个主要功能：

### 1. 中文标签自动转拼音功能

#### 功能描述
- 在进行标注时，如果输入的标签包含中文，系统会自动将其转换为驼峰式拼音格式
- 例如：输入"称号"会自动转换为"chengHao"
- 转换后的拼音标签会自动保存到预设标签列表中

#### 实现细节
- 新增了`libs/pinyin_utils.py`模块，包含中文转拼音的核心功能
- 支持pypinyin库进行高质量的拼音转换
- 提供备用映射表，在pypinyin库不可用时使用
- 修改了`new_shape()`方法，在添加新标签时自动处理中文转换

#### 使用方法
1. 在标注时正常输入中文标签
2. 系统会自动检测中文字符并转换为拼音
3. 转换结果会显示在控制台中
4. 转换后的标签会自动添加到预设标签列表

### 2. 预设标签自动保存功能

#### 功能描述
- 新添加的标签会自动保存到`data/predefined_classes.txt`文件中
- 确保标签在程序重启后仍然可用
- 自动去重，避免重复标签

#### 实现细节
- 新增了`save_predefined_classes()`方法
- 在`new_shape()`方法中添加了自动保存逻辑
- 更新默认标签下拉框以反映新添加的标签

### 3. 清空预设标签功能

#### 功能描述
- 在UI界面上添加了"清空预设标签"按钮
- 点击按钮可以清空所有预设标签
- 提供确认对话框，防止误操作

#### 实现细节
- 新增了清空预设标签的按钮到UI布局中
- 实现了`clear_predefined_classes()`和`clear_predefined_classes_with_confirmation()`方法
- 清空操作会同时清理内存中的标签历史和文件中的预设标签

## 修改的文件

### 1. `labelImg.py`
- 导入了中文转拼音工具模块
- 在`__init__`方法中添加了预设类文件路径存储
- 修改了`new_shape()`方法，添加中文转拼音和自动保存功能
- 添加了清空预设标签的UI按钮
- 新增了保存和清空预设标签的方法

### 2. `libs/pinyin_utils.py` (新文件)
- 实现了中文字符检测功能
- 实现了中文转拼音功能（支持pypinyin库和备用映射表）
- 提供了标签文本处理的统一接口

## 使用说明

### 安装依赖
为了获得最佳的中文转拼音效果，建议安装pypinyin库：
```bash
pip install pypinyin
```

如果不安装pypinyin库，系统会使用内置的备用映射表。

### 功能使用

#### 中文标签转拼音
1. 启动labelImg程序
2. 加载图片进行标注
3. 在标签输入框中输入中文标签（如"称号"）
4. 系统会自动转换为拼音格式（如"chengHao"）
5. 转换后的标签会自动保存到预设列表中

#### 清空预设标签
1. 在标签列表区域找到"清空预设标签"按钮
2. 点击按钮
3. 在确认对话框中选择"是"
4. 所有预设标签将被清空

## 技术特点

### 1. 智能转换
- 自动检测输入文本是否包含中文
- 只对包含中文的标签进行转换
- 保持非中文标签不变

### 2. 驼峰格式
- 转换结果采用驼峰命名格式
- 第一个拼音词小写，后续词首字母大写
- 符合编程命名规范

### 3. 容错处理
- 提供备用转换方案
- 异常情况下保持原始标签不变
- 用户友好的错误提示

### 4. 自动保存
- 新标签自动保存到文件
- 程序重启后标签仍然可用
- 自动去重避免重复

## 测试建议

1. 测试中文转拼音功能：
   - 输入常见中文词汇（如"人物"、"汽车"等）
   - 验证转换结果是否正确
   - 检查是否正确保存到预设列表

2. 测试自动保存功能：
   - 添加新标签后重启程序
   - 验证标签是否仍在预设列表中

3. 测试清空功能：
   - 点击清空按钮
   - 验证确认对话框是否正常显示
   - 确认清空后标签列表为空

## 注意事项

1. 建议安装pypinyin库以获得更好的转换效果
2. 清空预设标签操作不可撤销，请谨慎使用
3. 中文转拼音可能存在多音字问题，建议人工检查重要标签
4. 备用映射表只包含常用汉字，复杂汉字可能转换效果不佳
