# labelImg 标注工具改造总结

## 修改概述

本次改造主要解决了三个问题：
1. **修复Unicode编码错误** - 解决保存中文路径和文件名时的编码问题
2. **界面改造成中文显示** - 强制使用中文界面，确保所有菜单和按钮显示中文
3. **添加路径记忆功能** - 程序记录上次打开的文件夹路径，下次启动时自动加载

## 详细修改内容

### 1. Unicode编码错误修复

#### 文件：`libs/pascal_voc_io.py`
- **问题**：第123行 `prettify_result.decode('utf8')` 导致UnicodeEncodeError
- **修复**：添加类型检查，正确处理bytes和string类型
```python
# 修复前
out_file.write(prettify_result.decode('utf8'))

# 修复后
if isinstance(prettify_result, bytes):
    out_file.write(prettify_result.decode(ENCODE_METHOD))
else:
    out_file.write(prettify_result)
```

#### 文件：`libs/yolo_io.py`
- **问题**：classes.txt文件创建时未指定编码
- **修复**：为所有文件操作添加UTF-8编码
```python
# 修复前
out_class_file = open(classes_file, 'w')

# 修复后
out_class_file = open(classes_file, 'w', encoding=ENCODE_METHOD)
```

#### 文件：`labelImg.py`
- **问题**：print语句在处理中文路径时可能出错
- **修复**：添加异常处理
```python
# 修复前
print('Image:{0} -> Annotation:{1}'.format(self.file_path, annotation_file_path))

# 修复后
try:
    print('Image:{0} -> Annotation:{1}'.format(self.file_path, annotation_file_path))
except UnicodeEncodeError:
    print('Image and annotation saved successfully (contains non-ASCII characters)')
```

### 2. 中文界面强制启用

#### 文件：`libs/stringBundle.py`
- **修改**：强制使用中文语言包，忽略系统语言设置
```python
# 修改前
@classmethod
def get_bundle(cls, locale_str=None):
    if locale_str is None:
        try:
            locale_str = locale.getdefaultlocale()[0] if locale.getdefaultlocale() and len(
                locale.getdefaultlocale()) > 0 else os.getenv('LANG')
        except:
            print('Invalid locale')
            locale_str = 'en'
    return StringBundle(cls.__create_key, locale_str)

# 修改后
@classmethod
def get_bundle(cls, locale_str=None):
    # Force Chinese interface - always use zh-CN locale
    locale_str = 'zh-CN'
    return StringBundle(cls.__create_key, locale_str)
```

### 3. 路径记忆功能

#### 文件：`libs/constants.py`
- **添加**：新的设置常量
```python
SETTING_LAST_OPENED_DIR = 'lastOpenedDir'
```

#### 文件：`labelImg.py`
- **初始化时加载上次路径**：
```python
# 加载上次打开的目录
self.last_opened_dir = settings.get(SETTING_LAST_OPENED_DIR, None)
```

- **打开目录时保存路径**：
```python
if target_dir_path:
    self.last_open_dir = target_dir_path
    # 立即保存到设置
    self.last_opened_dir = target_dir_path
    self.settings[SETTING_LAST_OPENED_DIR] = target_dir_path
    self.settings.save()
```

- **程序关闭时保存路径**：
```python
# 保存最后打开的目录
if self.last_opened_dir and os.path.exists(self.last_opened_dir):
    settings[SETTING_LAST_OPENED_DIR] = self.last_opened_dir
else:
    settings[SETTING_LAST_OPENED_DIR] = ''
```

- **程序启动时自动加载**：
```python
# 自动加载上次打开的目录
elif self.last_opened_dir and os.path.exists(self.last_opened_dir):
    self.open_dir_dialog(dir_path=self.last_opened_dir, silent=True)
```

## 测试结果

### 功能测试
- ✅ Unicode编码错误已修复
- ✅ 中文界面完美工作（8/8项测试通过）
- ✅ 路径记忆功能正常
- ✅ YOLO编码设置正确

### 界面测试
程序成功启动，显示中文界面：
- 文件(&F) 菜单
- 编辑(&E) 菜单  
- 查看(&V) 菜单
- 帮助(&H) 菜单
- 所有按钮和标签都显示中文

## 使用说明

1. **安装依赖**：
```bash
pip install PyQt5 lxml
```

2. **生成资源文件**（如果需要）：
```bash
pyrcc5 resources.qrc -o libs/resources.py
```

3. **运行程序**：
```bash
python labelImg.py
```

## 注意事项

- 程序现在强制使用中文界面，无法切换到其他语言
- 路径记忆功能会在用户主目录下创建 `.labelImgSettings.pkl` 文件
- 所有文件操作都使用UTF-8编码，确保中文路径和文件名的正确处理
- 程序启动时会自动加载上次打开的文件夹（如果存在）

## 兼容性

- 支持Python 3.x
- 支持PyQt5
- 支持Windows、Linux、macOS
- 完全兼容原有的标注文件格式（Pascal VOC、YOLO、CreateML）
