History
=======

1.8.6 (2021-10-10)
------------------

* Display box width and height


1.8.5 (2021-04-11)
------------------

* Merged a couple of PRs
* Fixed issues
* Support CreateML format


1.8.4 (2020-11-04)
------------------

* Merged a couple of PRs
* Fixed issues

1.8.2 (2018-12-02)
------------------

* Fix pip depolyment issue


1.8.1 (2018-12-02)
------------------

* Fix issues
* Support zh-Tw strings


1.8.0 (2018-10-21)
------------------

* Support drawing sqaure rect
* Add item single click slot
* Fix issues

1.7.0 (2018-05-18)
------------------

* Support YOLO
* Fix minor issues


1.6.1 (2018-04-17)
------------------

* Fix issue

1.6.0 (2018-01-29)
------------------

* Add more pre-defined labels
* Show cursor pose in status bar
* Fix minor issues

1.5.2 (2017-10-24)
------------------

* Assign different colors to different lablels

1.5.1 (2017-9-27)
------------------

* Show a autosaving dialog

1.5.0 (2017-9-14)
------------------

* Fix the issues
* Add feature: Draw a box easier


1.4.3 (2017-08-09)
------------------

* Refactor setting
* Fix the issues


1.4.0 (2017-07-07)
------------------

* Add feature: auto saving
* Add feature: single class mode
* Fix the issues

1.3.4 (2017-07-07)
------------------

* Fix issues and improve zoom-in

1.3.3 (2017-05-31)
------------------

* Fix issues

1.3.2 (2017-05-18)
------------------

* Fix issues


1.3.1 (2017-05-11)
------------------

* Fix issues

1.3.0 (2017-04-22)
------------------

* Fix issues
* Add difficult tag
* Create new files for pypi

1.2.3 (2017-04-22)
------------------

* Fix issues

1.2.2 (2017-01-09)
------------------

* Fix issues
