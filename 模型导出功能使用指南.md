# 模型导出功能使用指南

## 功能概述
labelImg现在支持将YOLO模型导出为其他格式，包括ONNX、TensorRT、CoreML和TensorFlow Lite。

## 使用方法

### 1. 打开模型导出对话框
- 方法1: 在文件菜单中选择"导出模型"
- 方法2: 使用快捷键 Ctrl+Shift+M

### 2. 选择模型文件
- 点击"浏览..."按钮选择YOLO模型文件(.pt格式)
- 支持YOLOv8、YOLOv11等模型

### 3. 选择导出格式
- ONNX (.onnx): 跨平台推理格式
- TensorRT (.engine): NVIDIA GPU优化格式
- CoreML (.mlmodel): Apple设备格式
- TensorFlow Lite (.tflite): 移动端格式

### 4. 配置导出参数
- ONNX格式:
  - Opset版本 (默认12)
  - 动态batch大小
  - 模型简化
- TensorRT格式:
  - 精度模式 (FP16/FP32)
  - 工作空间大小

### 5. 设置输出
- 选择输出目录
- 设置输出文件名

### 6. 开始导出
- 点击"开始导出"按钮
- 查看进度和日志信息
- 等待导出完成

## 注意事项
1. 确保已安装ultralytics库: `pip install ultralytics`
2. TensorRT导出需要NVIDIA GPU和TensorRT库
3. 导出过程可能需要几分钟时间
4. 支持中英文界面

## 故障排除
- 如果导出失败，请检查模型文件是否有效
- 确保有足够的磁盘空间
- 检查网络连接（首次使用可能需要下载依赖）
