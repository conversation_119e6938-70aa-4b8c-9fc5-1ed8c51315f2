# labelImg 打包路径问题调试版本

## 问题描述
打包后运行程序提示：`Not find:/data/predefined_classes.txt (optional)`

## 修复内容

### 1. 代码逻辑修复
- **问题**：`load_predefined_classes`方法使用了错误的参数`default_prefdef_class_file`（可能为None）
- **修复**：改为使用`self.predefined_classes_file`（经过`get_resource_path`处理的正确路径）

### 2. PyInstaller配置修复
- **问题**：`labelImg.spec`文件中`datas=[]`为空，没有包含data文件夹
- **修复**：添加`datas=[('data', 'data'), ('resources', 'resources')]`

### 3. 添加详细调试信息

#### 启动时的环境信息
```
[DEBUG] ========== labelImg 启动调试信息 ==========
[DEBUG] Python版本: ...
[DEBUG] 当前工作目录: ...
[DEBUG] 脚本文件路径: ...
[DEBUG] PyInstaller环境检测到 (或 开发环境检测到)
[DEBUG] _MEIPASS路径: ... (如果是打包环境)
```

#### 资源路径解析过程
```
[DEBUG] PyInstaller环境检测到，使用_MEIPASS路径: C:\temp\...
[DEBUG] 资源文件完整路径: C:\temp\...\data\predefined_classes.txt
[DEBUG] 资源文件是否存在: True/False
```

#### 预设类文件加载过程
```
[DEBUG] 初始化预设类文件路径...
[DEBUG] default_prefdef_class_file参数: None
[DEBUG] 最终预设类文件路径: C:\temp\...\data\predefined_classes.txt
[DEBUG] 开始加载预设类文件...
[DEBUG] load_predefined_classes被调用
[DEBUG] 传入的文件路径: C:\temp\...\data\predefined_classes.txt
[DEBUG] 文件路径类型: <class 'str'>
[DEBUG] 检查文件是否存在: C:\temp\...\data\predefined_classes.txt
[DEBUG] 文件存在检查结果: True/False
```

#### 成功时的输出
```
[DEBUG] 开始读取文件内容...
[DEBUG] 成功读取 16 行标签
[DEBUG] 检查标签历史记录...
[DEBUG] 标签历史记录包含 16 个标签
[DEBUG] 第一个标签: dog
```

#### 失败时的详细诊断
```
[DEBUG] 文件不存在，尝试列出目录内容...
[DEBUG] 父目录存在: C:\temp\...\data
[DEBUG] 父目录内容: ['other_file.txt']
[DEBUG] 当前工作目录: C:\Users\<USER>