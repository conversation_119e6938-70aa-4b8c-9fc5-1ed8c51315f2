# 🚀 labelImg 集成版本运行指南

## 📋 快速开始

### 1. 运行主程序
```bash
python labelImg.py
```

### 2. 新功能快速体验

#### 🤖 AI助手功能
1. **查看AI助手面板**: 启动后默认显示在右侧停靠窗口
2. **加载YOLO模型**: 点击"选择模型"按钮，选择.pt模型文件
3. **AI预测**: 
   - 单图预测: `Ctrl+P` 或点击"预测当前"按钮
   - 批量预测: `Ctrl+Shift+P` 或点击"批量预测"按钮
4. **调整参数**: 使用滑块调整置信度、NMS阈值等参数

#### 📦 批量操作功能
1. **打开批量操作**: `Ctrl+B` 或菜单"工具" → "批量操作"
2. **选择操作类型**: 复制、删除、调整、转换、过滤
3. **配置参数**: 根据操作类型设置相应参数
4. **执行操作**: 点击"开始操作"按钮

#### ⌨️ 快捷键配置
1. **打开配置**: `Ctrl+K` 或菜单"工具" → "快捷键配置"
2. **查看快捷键**: 按分类浏览所有可用快捷键
3. **修改快捷键**: 双击快捷键进行编辑
4. **导入导出**: 备份和恢复快捷键配置

## 🎯 主要快捷键

### AI助手
- `Ctrl+P`: AI预测当前图像
- `Ctrl+Shift+P`: AI批量预测
- `F9`: 切换AI助手面板显示

### 批量操作
- `Ctrl+B`: 打开批量操作对话框
- `Ctrl+Shift+C`: 快速批量复制
- `Ctrl+Shift+D`: 快速批量删除

### 系统功能
- `Ctrl+K`: 快捷键配置
- `Ctrl+Shift+T`: 切换标签面板
- `Ctrl+Shift+R`: 切换矩形绘制模式

## 🔧 界面布局

### 停靠窗口组织
```
右侧面板 (标签化显示):
├── 🏷️ 标签面板 (原有功能)
├── 🤖 AI助手面板 (新增，默认显示)
└── 📁 文件面板 (原有功能)
```

### 工具栏布局
```
主工具栏:
├── 📁 文件操作组
├── 🔄 导航操作组  
├── ✏️ 编辑操作组
├── 🔍 视图操作组
├── 🤖 AI助手工具组 (新增)
├── 📦 批量操作工具组 (新增)
└── ⚙️ 模式切换组
```

## 📊 功能验证

### 测试新功能
1. **运行测试脚本**:
   ```bash
   python test_integration.py
   ```

2. **检查测试结果**: 确保所有项目显示 ✅

### 手动验证
1. **AI助手面板**: 检查是否正确显示在右侧
2. **工具菜单**: 验证新增的工具菜单项
3. **工具栏按钮**: 确认AI助手和批量操作按钮
4. **快捷键**: 测试主要快捷键是否响应

## 🐛 常见问题

### 1. AI助手面板不显示
- **解决方案**: 按 `F9` 切换AI面板显示，或在"视图"菜单中启用

### 2. 快捷键不响应
- **解决方案**: 打开快捷键配置 (`Ctrl+K`) 检查是否有冲突

### 3. 批量操作失败
- **解决方案**: 检查文件路径和权限，确保目标目录可写

### 4. YOLO模型加载失败
- **解决方案**: 确保模型文件格式正确(.pt)，路径无中文字符

## 📈 性能优化建议

### 1. 大数据集处理
- 使用批量预测时，建议单次处理不超过1000张图片
- 对于超大数据集，可分批处理

### 2. 内存管理
- 定期关闭不需要的面板
- 处理完大批量操作后重启程序

### 3. 模型选择
- 根据硬件配置选择合适的YOLO模型
- GPU加速可显著提升预测速度

## 🔄 更新和维护

### 配置文件位置
```
Windows: C:\Users\<USER>\AppData\Roaming\labelImg\
Linux/Mac: ~/.labelImg/

配置文件:
├── predefined_classes.txt (预设标签)
├── shortcuts.json (快捷键配置)
└── ai_settings.yaml (AI设置，待实现)
```

### 备份建议
- 定期备份预设标签文件
- 导出快捷键配置作为备份
- 保存重要的AI模型文件

## 🎉 享受新功能

现在您可以享受labelImg的全新体验：
- 🤖 **AI智能预标注**: 大幅提升标注效率
- 📦 **批量操作**: 轻松处理大规模数据
- ⌨️ **个性化快捷键**: 优化操作流程
- 🎨 **现代化界面**: 更好的用户体验

祝您使用愉快！ 🚀
