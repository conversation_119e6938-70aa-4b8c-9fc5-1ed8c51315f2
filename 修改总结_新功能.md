# labelImg标注工具改造总结

## 改造需求
- 程序默认大小为：1366*768
- 程序启动的时候，自动居中
- 程序默认就勾选自动保存

## 实现的修改

### 1. 默认窗口大小设置为1366x768

#### 文件：`labelImg.py`
- **位置**：第488行
- **修改前**：
```python
size = settings.get(SETTING_WIN_SIZE, QSize(600, 500))
```
- **修改后**：
```python
size = settings.get(SETTING_WIN_SIZE, QSize(1366, 768))
```

### 2. 程序启动时自动居中

#### 文件：`labelImg.py`
- **位置**：第488-511行
- **修改内容**：
```python
size = settings.get(SETTING_WIN_SIZE, QSize(1366, 768))
position = QPoint(0, 0)
saved_position = settings.get(SETTING_WIN_POSE, position)

# Check if there's a saved position and it's valid
has_valid_saved_position = False
for i in range(QApplication.desktop().screenCount()):
    if QApplication.desktop().availableGeometry(i).contains(saved_position):
        position = saved_position
        has_valid_saved_position = True
        break

self.resize(size)

# If no valid saved position, center the window
if not has_valid_saved_position:
    # Get the primary screen geometry
    screen = QApplication.desktop().screenGeometry()
    # Calculate center position
    x = (screen.width() - size.width()) // 2
    y = (screen.height() - size.height()) // 2
    position = QPoint(x, y)

self.move(position)
```

**功能说明**：
- 检查是否有有效的保存位置
- 如果没有有效的保存位置，则计算屏幕中心位置
- 将窗口移动到计算出的居中位置

### 3. 自动保存默认勾选

#### 文件：`labelImg.py`
- **位置**：第418行
- **修改前**：
```python
self.auto_saving.setChecked(settings.get(SETTING_AUTO_SAVE, False))
```
- **修改后**：
```python
self.auto_saving.setChecked(settings.get(SETTING_AUTO_SAVE, True))
```

## 修改验证

所有修改已通过代码检查验证：

1. ✅ **默认窗口大小**：已设置为1366x768
2. ✅ **窗口居中功能**：已添加居中逻辑
3. ✅ **自动保存默认勾选**：已设置默认值为True

## 使用说明

1. **首次启动**：程序将以1366x768的大小在屏幕中央显示，自动保存功能默认开启
2. **后续启动**：如果之前保存了窗口位置，程序将恢复到上次的位置；否则仍会居中显示
3. **自动保存**：程序启动时自动保存选项默认为勾选状态，用户可以手动取消勾选

## 技术细节

- 使用`QApplication.desktop().screenGeometry()`获取屏幕尺寸
- 通过计算`(screen_width - window_width) // 2`和`(screen_height - window_height) // 2`实现居中
- 保持了原有的多显示器支持功能
- 修改了设置的默认值，不影响现有用户的保存设置

## 兼容性

- 兼容PyQt4和PyQt5
- 支持多显示器环境
- 保持向后兼容，不影响现有用户的设置文件
