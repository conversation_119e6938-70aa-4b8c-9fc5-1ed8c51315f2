# AI助手面板训练模型选择功能修复说明

## 问题描述
在AI助手面板的开始训练中，训练参数的模型大小只能选择YOLO官方的模型（yolov8n, yolov8s, yolov8m, yolov8l），用户自己训练后的模型并没有在选择列表中，需要支持自定义模型选择和手动指定模型路径。

## 修复内容

### 1. 训练参数标签页界面改进 (`libs/ai_assistant_panel.py`)

#### 新增功能：
- **模型类型选择**：支持三种模型类型
  - 预训练模型：官方YOLO模型（yolov8n, yolov8s, yolov8m, yolov8l, yolov8x）
  - 自定义模型：扫描models目录下的用户训练模型
  - 手动指定：通过文件浏览器选择任意位置的模型文件

#### 新增UI组件：
- `model_type_combo`：模型类型选择下拉框
- `pretrained_combo`：预训练模型选择下拉框
- `custom_combo`：自定义模型选择下拉框
- `manual_model_edit`：手动模型路径输入框
- `refresh_models_btn`：刷新模型列表按钮

#### 新增方法：
- `on_model_type_changed()`：处理模型类型切换
- `refresh_training_models()`：刷新自定义模型列表
- `browse_manual_model()`：浏览选择模型文件
- `get_selected_training_model()`：获取当前选择的模型信息

### 2. 训练配置数据结构更新 (`libs/ai_assistant/yolo_trainer.py`)

#### TrainingConfig类修改：
```python
# 原来的字段
model_size: str  # 模型大小 (yolov8n, yolov8s, etc.)

# 修改为新的字段
model_type: str  # 模型类型 (pretrained, custom, manual)
model_path: str  # 模型路径
model_name: str  # 模型名称
```

#### 训练器模型加载逻辑改进：
- 支持根据模型类型加载不同来源的模型
- 增加模型文件存在性验证
- 增加模型文件大小显示
- 增加详细的日志输出

### 3. 日志功能增强

#### 新增日志内容：
- 模型类型切换日志
- 自定义模型扫描日志
- 模型文件验证日志
- 模型加载过程日志
- 错误处理日志

#### 日志示例：
```
🔄 切换模型类型: 自定义模型
🔍 正在扫描自定义模型...
✅ 找到 3 个自定义模型
   📄 my_custom_model.pt
   📄 best_model_v2.pt
   📄 trained_model.pt
🤖 模型配置信息:
   模型类型: custom
   模型名称: my_custom_model.pt
   模型路径: models/custom/my_custom_model.pt
📊 模型文件大小: 14.2 MB
✅ 模型加载成功
```

## 使用方法

### 1. 使用预训练模型
1. 在训练参数页面，模型类型选择"预训练模型"
2. 从下拉列表中选择官方模型（yolov8n, yolov8s, yolov8m, yolov8l, yolov8x）

### 2. 使用自定义模型
1. 将训练好的模型文件放入`models/`目录或`models/custom/`目录
2. 在训练参数页面，模型类型选择"自定义模型"
3. 点击"🔄 刷新模型列表"按钮
4. 从下拉列表中选择您的自定义模型

### 3. 手动指定模型
1. 在训练参数页面，模型类型选择"手动指定"
2. 点击"📁 浏览"按钮选择模型文件
3. 或直接在输入框中输入模型文件的完整路径

## 技术细节

### 模型扫描逻辑
- 扫描`models/`目录下的所有`.pt`、`.onnx`、`.engine`文件
- 过滤掉官方预训练模型，只显示用户自定义模型
- 支持`models/custom/`子目录中的模型
- 显示模型来源标识（[自定义]、[用户]）

### 错误处理
- 模型文件不存在时的提示
- 模型加载失败时的错误信息
- 模型管理器未初始化时的处理
- 文件权限问题的处理

### 兼容性
- 保持与现有代码的兼容性
- 支持所有YOLO支持的模型格式
- 向后兼容原有的训练配置

## 测试建议

1. **预训练模型测试**：选择不同的官方模型进行训练
2. **自定义模型测试**：使用之前训练好的模型作为基础模型
3. **手动指定测试**：选择不同位置的模型文件
4. **错误处理测试**：测试不存在的文件路径、无效的模型文件等
5. **界面交互测试**：测试模型类型切换、刷新按钮等功能

## 注意事项

1. 确保模型文件格式正确（.pt, .onnx, .engine）
2. 自定义模型应该与当前数据集的类别数量匹配
3. 大型模型文件可能需要更长的加载时间
4. 建议将常用的自定义模型放在`models/custom/`目录中便于管理
