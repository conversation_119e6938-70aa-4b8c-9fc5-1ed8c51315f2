# labelImg标注工具功能修复总结

## 修复需求分析

用户提出了三个功能问题需要修复：

1. **全部重置后程序退出问题**：点击菜单中的`全部重置`菜单按钮后，程序自动退出，希望退出后能自动启动
2. **重启后程序不居中显示**：全部重置后，程序重新启动后，程序不是居中显示
3. **打开文件夹重复弹框问题**：点击UI界面中的`打开文件夹`按钮，选择完图片文件夹后，还会再弹出一次`save annotations to the directory`的选择框

## 修复实现

### 1. 修复reset_all方法实现自动重启 ✅

**文件位置**：`labelImg.py` 第2819-2846行

**修复前问题**：
- 程序只是简单调用`self.close()`退出
- 用户需要手动重新启动程序

**修复后功能**：
- 添加确认对话框，避免误操作
- 自动检测启动方式（Python脚本 vs 可执行文件）
- 保持启动参数，确保重启后功能一致
- 用户体验更加流畅

**核心代码**：
```python
def reset_all(self):
    """重置所有设置并自动重启程序"""
    # 显示确认对话框
    reply = QMessageBox.question(self, '确认重置',
                                 '确定要重置所有设置吗？程序将自动重启。',
                                 QMessageBox.Yes | QMessageBox.No,
                                 QMessageBox.No)
    
    if reply == QMessageBox.Yes:
        # 重置设置
        self.settings.reset()
        
        # 获取当前程序的启动参数
        import sys
        current_args = sys.argv[:]
        
        # 关闭当前程序
        self.close()
        
        # 启动新的程序实例
        process = QProcess()
        if current_args[0].endswith('.py'):
            # 如果是Python脚本，使用Python解释器启动
            python_exe = sys.executable
            process.startDetached(python_exe, current_args)
        else:
            # 如果是可执行文件，直接启动
            process.startDetached(current_args[0], current_args[1:])
```

### 2. 修复重启后窗口居中问题 ✅

**文件位置**：`labelImg.py` 第1008-1036行

**修复前问题**：
- 重启后使用之前保存的窗口位置
- 重置设置后仍然记住旧位置

**修复后功能**：
- 智能检测是否为首次启动或重置后启动
- 重置后强制居中显示
- 保持正常使用时的位置记忆功能

**核心代码**：
```python
# 检查是否是重置后的首次启动（设置文件不存在或为空）
is_fresh_start = not os.path.exists(settings.path) or len(settings.data) == 0

# Check if there's a saved position and it's valid (only if not fresh start)
has_valid_saved_position = False
if not is_fresh_start:
    for i in range(QApplication.desktop().screenCount()):
        if QApplication.desktop().availableGeometry(i).contains(saved_position):
            position = saved_position
            has_valid_saved_position = True
            break

# If no valid saved position or fresh start, center the window
if not has_valid_saved_position or is_fresh_start:
    # Get the primary screen geometry
    screen = QApplication.desktop().screenGeometry()
    # Calculate center position
    x = (screen.width() - size.width()) // 2
    y = (screen.height() - size.height()) // 2
    position = QPoint(x, y)
```

### 3. 优化打开文件夹流程 ✅

**文件位置**：`labelImg.py` 第2624-2643行

**修复前问题**：
- 选择图片文件夹后还会弹出保存目录选择框
- 用户需要进行两次目录选择操作

**修复后功能**：
- 直接将图片目录设为标注保存目录
- 取消第二次目录选择对话框
- 在状态栏显示保存目录信息
- 简化用户操作流程

**核心代码**：
```python
if target_dir_path:
    self.last_open_dir = target_dir_path
    # Save the last opened directory to settings immediately
    self.last_opened_dir = target_dir_path
    self.settings[SETTING_LAST_OPENED_DIR] = target_dir_path
    self.settings.save()
    
    # 直接将选择的图片目录设为标注保存目录，不再弹出第二次对话框
    self.default_save_dir = target_dir_path
    
    # 更新状态栏显示
    self.statusBar().showMessage('%s . Annotation will be saved to %s' %
                                 ('Open Directory', self.default_save_dir))
    self.statusBar().show()
```

## 修复验证

所有修复功能已通过代码检查验证：

1. ✅ **reset_all方法自动重启**：包含确认对话框、设置重置、参数获取、进程启动等完整逻辑
2. ✅ **窗口居中逻辑**：添加首次启动检测，重置后强制居中显示
3. ✅ **打开文件夹流程**：直接设置保存目录，取消重复对话框
4. ✅ **代码语法检查**：所有修改符合Python语法规范

## 使用说明

### 全部重置功能
1. 点击菜单 `文件` → `全部重置`
2. 确认对话框中点击"是"
3. 程序自动重启并居中显示

### 打开文件夹功能
1. 点击工具栏的`打开文件夹`按钮
2. 选择包含图片的文件夹
3. 程序自动将该文件夹设为标注保存目录
4. 状态栏显示保存目录信息

### 窗口位置
- 首次启动：自动居中显示
- 重置后启动：强制居中显示
- 正常使用：记住上次窗口位置

## 技术细节

- **进程管理**：使用`QProcess.startDetached()`确保新进程独立运行
- **设置检测**：通过检查设置文件存在性和数据长度判断是否为首次启动
- **用户体验**：添加确认对话框和状态栏提示，提高操作友好性
- **兼容性**：支持Python脚本和打包后的可执行文件两种启动方式

## 总结

本次修复解决了用户提出的三个核心问题，提升了labelImg工具的用户体验：
- 重置功能更加智能和便捷
- 窗口显示更加合理
- 文件夹操作更加简化

所有修复都保持了原有功能的完整性，同时增强了程序的易用性。
